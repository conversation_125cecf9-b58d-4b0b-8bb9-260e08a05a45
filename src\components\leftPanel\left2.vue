<!-- 今日巡检情况 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>今日巡检情况</span>
        <span class="header-subtitle">INSPECTION OF TODAY</span>
      </div>
    </template>
    <template #content>
      <div class="inspection-container">
        <div class="charts-row">
          <!-- 桥梁 -->
          <div class="chart-section">
            <div class="section-title" :style="{
              backgroundImage: `url(${BgPanel1TopBox})`,
              backgroundSize: '100% 100%',
              backgroundRepeat: 'no-repeat'
            }">
              <p class="title-text">桥梁</p>
            </div>

            <div class="chart-content">
              <div class="chart-wrapper">
                <CEcharts ref="bridgeChartRef" :option="chart1Options" @onload="onBridgeChartMounted"
                  class="chart-instance" />
                <div class="chart-center-text">
                  <div class="percentage">{{ getBridgePercentage() }}%</div>
                  <div class="label">已巡检</div>
                </div>
              </div>

              <div class="legend-info">
                <div class="legend-item">
                  <div class="legend-color inspected"></div>
                  <div class="legend-content">
                    <span class="legend-label">已巡检</span>
                    <span class="legend-value">{{ chartData.bridge[0]?.value || 0 }}个</span>
                  </div>
                </div>
                <div class="legend-item">
                  <div class="legend-color not-inspected"></div>
                  <div class="legend-content">
                    <span class="legend-label">未巡检</span>
                    <span class="legend-value">{{ chartData.bridge[1]?.value || 0 }}个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 隧道 -->
          <div class="chart-section">
            <div class="section-title" :style="{
              backgroundImage: `url(${BgPanel1TopBox})`,
              backgroundSize: '100% 100%',
              backgroundRepeat: 'no-repeat'
            }">
              <p class="title-text">隧道</p>
            </div>

            <div class="chart-content">
              <div class="chart-wrapper">
                <CEcharts ref="tunnelChartRef" :option="chart2Options" @onload="onTunnelChartMounted"
                  class="chart-instance" />
                <div class="chart-center-text">
                  <div class="percentage">{{ getTunnelPercentage() }}%</div>
                  <div class="label">已巡检</div>
                </div>
              </div>

              <div class="legend-info">
                <div class="legend-item">
                  <div class="legend-color inspected"></div>
                  <div class="legend-content">
                    <span class="legend-label">已巡检</span>
                    <span class="legend-value">{{ chartData.tunnel[0]?.value || 0 }}个</span>
                  </div>
                </div>
                <div class="legend-item">
                  <div class="legend-color not-inspected"></div>
                  <div class="legend-content">
                    <span class="legend-label">未巡检</span>
                    <span class="legend-value">{{ chartData.tunnel[1]?.value || 0 }}个</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'
import BgPanel1TopBox from '@/assets/images/bg_panel1_top_box.png'

// 图表实例引用
const bridgeChartRef = ref(null)
const tunnelChartRef = ref(null)
let bridgeChartInstance = null
let tunnelChartInstance = null

// 数据变化定时器
let dataTimer = null

// 生命周期 - 挂载
onMounted(() => {
  // 启动数据变化定时器
  startDataTimer()
})

// 生命周期 - 卸载
onUnmounted(() => {
  if (dataTimer) {
    clearInterval(dataTimer)
  }
})

/*---------------------------------- 图表数据相关 ---------------------------------*/
// 数据 - 图表数据源
const chartData = ref({
  bridge: [
    { label: '已巡检', value: 12 },
    { label: '未巡检', value: 3 }
  ],
  tunnel: [
    { label: '已巡检', value: 23 },
    { label: '未巡检', value: 2 }
  ]
})

// 方法 - 生成随机数据
const generateRandomData = () => {
  const bridgeInspected = Math.floor(Math.random() * 20) + 5
  const bridgeNotInspected = Math.floor(Math.random() * 10) + 1

  const tunnelInspected = Math.floor(Math.random() * 30) + 10
  const tunnelNotInspected = Math.floor(Math.random() * 8) + 1

  chartData.value = {
    bridge: [
      { label: '已巡检', value: bridgeInspected },
      { label: '未巡检', value: bridgeNotInspected }
    ],
    tunnel: [
      { label: '已巡检', value: tunnelInspected },
      { label: '未巡检', value: tunnelNotInspected }
    ]
  }

  // 更新图表配置
  chart1Options.value = createChart1Options()
  chart2Options.value = createChart2Options()
}

// 方法 - 启动数据变化定时器
const startDataTimer = () => {
  // 立即执行一次
  generateRandomData()

  // 每2秒变化一次数据
  dataTimer = setInterval(() => {
    generateRandomData()
  }, 2000)
}

// 方法 - 计算桥梁巡检百分比
const getBridgePercentage = () => {
  const inspected = chartData.value.bridge[0]?.value || 0
  const notInspected = chartData.value.bridge[1]?.value || 0
  const total = inspected + notInspected
  return total > 0 ? Math.round((inspected / total) * 100) : 0
}

// 方法 - 计算隧道巡检百分比
const getTunnelPercentage = () => {
  const inspected = chartData.value.tunnel[0]?.value || 0
  const notInspected = chartData.value.tunnel[1]?.value || 0
  const total = inspected + notInspected
  return total > 0 ? Math.round((inspected / total) * 100) : 0
}

/*---------------------------------- 图表相关配置 ---------------------------------*/
// 方法 - 创建图表1配置项
const createChart1Options = () => {
  return {
    color: ['#00ffaf', '#306fff'],
    tooltip: {
      show: false
    },
    legend: {
      show: false
    },
    series: [
      {
        name: '巡检情况',
        type: 'pie',
        center: ['50%', '50%'],
        radius: ['55%', '80%'],
        startAngle: 90,
        data: chartData.value.bridge,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: 'transparent'
        },
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 数据 - 图表1配置项
const chart1Options = ref(createChart1Options())

// 方法 - 创建图表2配置项
const createChart2Options = () => {
  return {
    color: ['#00ffaf', '#306fff'],
    tooltip: {
      show: false
    },
    legend: {
      show: false
    },
    series: [
      {
        name: '巡检情况',
        type: 'pie',
        center: ['50%', '50%'],
        radius: ['55%', '80%'],
        startAngle: 90,
        data: chartData.value.tunnel,
        label: {
          show: false
        },
        labelLine: {
          show: false
        },
        itemStyle: {
          borderWidth: 2,
          borderColor: 'transparent'
        },
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 数据 - 图表2配置项
const chart2Options = ref(createChart2Options())

// 钩子 - 桥梁图表挂载时
const onBridgeChartMounted = (echartsInstance) => {
  bridgeChartInstance = echartsInstance
  echartsInstance.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: 0
  })
}

// 钩子 - 隧道图表挂载时
const onTunnelChartMounted = (echartsInstance) => {
  tunnelChartInstance = echartsInstance
  echartsInstance.dispatchAction({
    type: 'highlight',
    seriesIndex: 0,
    dataIndex: 0
  })
}
</script>
<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.inspection-container {
  padding: 20px 16px 20px 0;
  /* 右边距与监测告警组件对齐 */
  height: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
}

.charts-row {
  display: flex;
  gap: 20px;
  height: 100%;
  align-items: center;
}

.chart-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
}

.section-title {
  width: 120px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  margin-right: 70px;

  .title-text {
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    letter-spacing: 5px;
    margin: 0;
  }
}

.chart-content {
  display: flex;
  align-items: center;
  gap: 20px;
  width: 100%;
  justify-content: center;
  flex: 1;
}

.chart-wrapper {
  position: relative;
  width: 150px;
  height: 150px;

  .chart-instance {
    width: 100%;
    height: 100%;
  }

  .chart-center-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;

    .percentage {
      font-size: 22px;
      font-weight: bold;
      color: #fff;
      line-height: 1;
      margin-bottom: 4px;
    }

    .label {
      font-size: 14px;
      color: #fff;
    }
  }
}

.legend-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
  min-width: 56px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;

  .legend-color {
    width: 4px;
    height: 40px;
    border-radius: 2px;

    &.inspected {
      background: #00ffaf;
    }

    &.not-inspected {
      background: #306fff;
    }
  }

  .legend-content {
    display: flex;
    flex-direction: column;
    gap: 4px;

    .legend-label {
      font-size: 13px;
      color: #fff;
    }

    .legend-value {
      font-size: 13px;
      color: #fff;
      font-weight: bold;
    }
  }
}
</style>

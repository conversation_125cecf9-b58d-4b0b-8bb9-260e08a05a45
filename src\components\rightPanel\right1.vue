<!-- 监测类型 -->
<template>
  <CPanel>
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>监测类型</span>
        <span class="header-subtitle">TYPE OF MONITORING</span>
      </div>
    </template>
    <template #content>
      <div class="monitor-types">
        <div v-for="(item, index) in monitorData" :key="index" class="monitor-item">
          <div class="monitor-info">
            <span class="monitor-name">{{ item.name }}</span>
            <span class="monitor-count">{{ item.count }}(个)</span>
          </div>
          <div class="monitor-bar">
            <div class="bar-fill" :style="{ width: item.percentage + '%' }"></div>
          </div>
        </div>
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref } from 'vue'
import CPanel from '@/components/common/CPanel.vue'

// 监测类型数据
const monitorData = ref([
  {
    name: '水质监测',
    count: 28,
    percentage: 75
  },
  {
    name: '水位监测',
    count: 15,
    percentage: 45
  },
  {
    name: '桥梁参流监测',
    count: 32,
    percentage: 88
  },
  {
    name: '桥梁变形监测',
    count: 26,
    percentage: 68
  },
  {
    name: '桥梁荷载监测',
    count: 19,
    percentage: 52
  },
])

</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #fff;
    margin-left: 8px;
  }
}

.monitor-types {
  padding: 16px 20px;
  height: 100%;
  min-height: 200px;
  overflow-y: auto;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 212, 255, 0.6);
    border-radius: 2px;

    &:hover {
      background: rgba(0, 212, 255, 0.8);
    }
  }
}

.monitor-item {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.monitor-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2px;
  margin-bottom: 6px;
}

.monitor-bar {
  width: 100%;
  height: 12px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);

  .bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #00D4FF 0%, #0099CC 50%, #00BFFF 100%);
    border-radius: 6px;
    transition: width 1.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 0 6px rgba(0, 212, 255, 0.4);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 50%;
      background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
      border-radius: 6px 6px 0 0;
    }

    &::after {
      content: '';
      position: absolute;
      top: 1px;
      right: 1px;
      width: 2px;
      height: calc(100% - 2px);
      background: rgba(255, 255, 255, 0.6);
      border-radius: 0 4px 4px 0;
    }
  }
}

.monitor-name {
  color: #E8F4FD;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.5px;
}

.monitor-count {
  color: #00D4FF;
  font-size: 14px;
  font-weight: 600;
  text-shadow: 0 0 4px rgba(0, 212, 255, 0.5);
  letter-spacing: 0.5px;
}
</style>

<template>
  <CPanel class="monitoring-alarm">
    <template #header>
      <div class="header-content">
        <div class="header-line"></div>
        <span>监测告警</span>
        <span class="header-subtitle">MONITORING AND ALARMING</span>
      </div>
    </template>
    <template #content>
      <div class="wh-full relative">
        <div class="date-picker-container">
          <div class="custom-date-picker" @click="toggleDatePicker">
            <span class="date-icon">📅</span>
            <span class="date-text">{{ formatDateRange() }}</span>
            <span class="arrow-icon">▼</span>
          </div>

          <div v-if="showDatePicker" class="date-picker-panel">
            <div class="date-input-group">
              <div class="date-input-item">
                <label>开始日期:</label>
                <input
                  type="date"
                  v-model="startDate"
                  @change="handleDateInputChange"
                  class="date-input"
                />
              </div>
              <div class="date-input-item">
                <label>结束日期:</label>
                <input
                  type="date"
                  v-model="endDate"
                  @change="handleDateInputChange"
                  class="date-input"
                />
              </div>
            </div>
            <div class="date-picker-buttons">
              <button @click="applyDateRange" class="apply-btn">确定</button>
              <button @click="closeDatePicker" class="cancel-btn">取消</button>
            </div>
          </div>
        </div>
        <CEcharts ref="chartRef" :option="option" class="monitoring-chart" />
      </div>
    </template>
  </CPanel>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import CPanel from '@/components/common/CPanel.vue'
import CEcharts from '@/components/common/CEcharts.vue'

const option = ref({})
const chartRef = ref()

// 日期格式化函数
const formatDate = (date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 获取几天前的日期
const getDateBefore = (days) => {
  const date = new Date()
  date.setDate(date.getDate() - days)
  return formatDate(date)
}

// 属性 - 筛选时间
const filterTime = ref([
  getDateBefore(6),
  formatDate(new Date())
])

// 日期选择器相关状态
const showDatePicker = ref(false)
const startDate = ref(getDateBefore(6))
const endDate = ref(formatDate(new Date()))

// 模拟数据生成函数
const generateMockData = (startDate, endDate) => {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const timeDiff = end.getTime() - start.getTime()
  const days = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1

  const data = []
  const labels = []

  for (let i = 0; i < days; i++) {
    const currentDate = new Date(start)
    currentDate.setDate(start.getDate() + i)
    labels.push(formatDate(currentDate))
    // 生成随机告警数据 (0-15次)
    data.push(Math.floor(Math.random() * 16))
  }

  return { data, labels }
}

// 数据 - 图表数据源
const chartData = ref([])

// 格式化日期范围显示
const formatDateRange = () => {
  return `${filterTime.value[0]} 至 ${filterTime.value[1]}`
}

// 切换日期选择器显示
const toggleDatePicker = () => {
  showDatePicker.value = !showDatePicker.value
  if (showDatePicker.value) {
    startDate.value = filterTime.value[0]
    endDate.value = filterTime.value[1]
  }
}

// 关闭日期选择器
const closeDatePicker = () => {
  showDatePicker.value = false
}

// 应用日期范围
const applyDateRange = () => {
  if (startDate.value && endDate.value) {
    filterTime.value = [startDate.value, endDate.value]
    handleDateChange()
    showDatePicker.value = false
  }
}

// 处理日期输入变化
const handleDateInputChange = () => {
  // 确保结束日期不早于开始日期
  if (startDate.value && endDate.value && new Date(endDate.value) < new Date(startDate.value)) {
    endDate.value = startDate.value
  }
}

// 方法 - 处理时间变化
const handleDateChange = () => {
  if (filterTime.value && filterTime.value.length === 2) {
    const mockData = generateMockData(filterTime.value[0], filterTime.value[1])
    chartData.value = mockData.data
    updateChart(mockData.data, mockData.labels)
  }
}

// 更新图表
const updateChart = (data, labels) => {
  option.value = createMonitoringChart(data, labels)
}

const createMonitoringChart = (data, labels) => {
  return {
    color: ['#00ffaf', '#306fff'],
    legend: {
      show: false
    },
    grid: {
      top: 40,
      bottom: 0,
      left: 10,
      right: 10,
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true, // 显示X轴线
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        fontSize: 10,
        color: '#fff',
        formatter: function(value) {
          return value.substring(5) // 只显示月-日
        }
      },
      // 确保折线从Y轴开始
      boundaryGap: false
    },
    yAxis: {
      type: 'value',
      name: '次',
      min: 0, // 确保Y轴从0开始
      nameTextStyle: {
        color: '#fff',
        padding: [0, 20, 0, 0]
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, 0.2)'
        }
      },
      axisLine: {
        show: true, // 显示Y轴线
        lineStyle: {
          color: '#fff'
        }
      },
      axisLabel: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '监测警告',
        type: 'line',
        data: data,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#00ffaf'
        },
        itemStyle: {
          color: '#00ffaf'
        },
        connectNulls: false,
        // 确保折线从Y轴开始
        areaStyle: null,
        // 添加起始点配置
        markPoint: {
          symbol: 'circle',
          symbolSize: 0,
          data: [
            {
              coord: [0, data[0] || 0],
              itemStyle: {
                opacity: 0
              }
            }
          ]
        }
      }
    ]
  }
}

// 点击外部关闭日期选择器
const handleClickOutside = (event) => {
  const datePickerContainer = event.target.closest('.date-picker-container')
  if (!datePickerContainer && showDatePicker.value) {
    showDatePicker.value = false
  }
}

onMounted(() => {
  // 初始化图表数据
  handleDateChange()
  // 添加全局点击事件监听
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 移除全局点击事件监听
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss" scoped>
.header-content {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #fff;

  .header-line {
    width: 4px;
    height: 20px;
    background: linear-gradient(180deg, #00D4FF 0%, #0099CC 100%);
    border-radius: 2px;
  }

  .header-subtitle {
    font-size: 12px;
    color: #C5D6E6;
    margin-left: 8px;
  }
}

.wh-full {
  width: 100%;
  height: 100%;
  min-height: 200px;
  padding: 0 16px 0 0; /* 右边距与上面组件对齐 */
}

.relative {
  position: relative;
}

.monitoring-chart {
  height: 100%; /* 使用100%高度以填充容器 */
  min-height: 200px; /* 设置最小高度 */
  width: 100%;
  margin-top: 10px; /* 顶部间距 */
}

.date-picker-container {
  position: absolute;
  right: 16px; /* 与容器右边距保持一致 */
  top: 0;
  z-index: 10;
}

.custom-date-picker {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  font-size: 12px;
  min-width: 220px;
  transition: all 0.3s ease;

  &:hover {
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.1);
  }

  .date-icon {
    font-size: 14px;
  }

  .date-text {
    flex: 1;
    color: #fff;
  }

  .arrow-icon {
    font-size: 10px;
    transition: transform 0.3s ease;
  }

  &.active .arrow-icon {
    transform: rotate(180deg);
  }
}

.date-picker-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 4px;
  background: rgba(0, 20, 40, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

.date-input-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.date-input-item {
  display: flex;
  align-items: center;
  gap: 8px;

  label {
    color: #fff;
    font-size: 12px;
    min-width: 60px;
  }

  .date-input {
    flex: 1;
    padding: 6px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    color: #fff;
    font-size: 12px;

    &:focus {
      outline: none;
      border-color: #00ffaf;
      box-shadow: 0 0 0 2px rgba(0, 255, 175, 0.2);
    }

    &::-webkit-calendar-picker-indicator {
      filter: invert(1);
      cursor: pointer;
    }
  }
}

.date-picker-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;

  button {
    padding: 6px 16px;
    border: none;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &.apply-btn {
      background: #00ffaf;
      color: #000;

      &:hover {
        background: #00e69c;
      }
    }

    &.cancel-btn {
      background: rgba(255, 255, 255, 0.1);
      color: #fff;
      border: 1px solid rgba(255, 255, 255, 0.2);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}
</style>
